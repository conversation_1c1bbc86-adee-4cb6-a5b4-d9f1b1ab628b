import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SecondaryButton } from '../../../frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton';
import { SecondaryButtonEventManager } from '../../../frontend/ButtonCodes/secondary/event/event_secondary';

describe('SecondaryButton', () => {
  // 基础渲染测试
  describe('基础渲染', () => {
    test('应该渲染默认按键文本', () => {
      render(<SecondaryButton />);
      expect(screen.getByRole('button')).toHaveTextContent('按键');
    });

    test('应该渲染自定义文本', () => {
      render(<SecondaryButton text="自定义按键" />);
      expect(screen.getByRole('button')).toHaveTextContent('自定义按键');
    });

    test('应该渲染子元素', () => {
      render(<SecondaryButton><span>子元素</span></SecondaryButton>);
      expect(screen.getByText('子元素')).toBeInTheDocument();
    });
  });

  // 持续状态模式测试
  describe('持续状态模式 (toggle)', () => {
    test('应该在点击时切换激活状态', () => {
      const onToggle = jest.fn();
      render(<SecondaryButton mode="toggle" onToggle={onToggle} />);
      
      const button = screen.getByRole('button');
      
      // 第一次点击 - 激活
      fireEvent.click(button);
      expect(onToggle).toHaveBeenCalledWith(true);
      
      // 第二次点击 - 取消激活
      fireEvent.click(button);
      expect(onToggle).toHaveBeenCalledWith(false);
    });

    test('应该正确显示激活状态样式', () => {
      render(<SecondaryButton mode="toggle" isActive={true} />);
      const button = screen.getByRole('button');
      
      expect(button).toHaveStyle({
        backgroundColor: '#929292'
      });
    });

    test('应该正确显示未激活状态样式', () => {
      render(<SecondaryButton mode="toggle" isActive={false} />);
      const button = screen.getByRole('button');
      
      expect(button).toHaveStyle({
        backgroundColor: '#f1f1f1'
      });
    });
  });

  // 瞬时状态模式测试
  describe('瞬时状态模式 (instant)', () => {
    test('应该在点击时触发onPress事件', () => {
      const onPress = jest.fn();
      render(<SecondaryButton mode="instant" onPress={onPress} />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(onPress).toHaveBeenCalled();
    });

    test('应该在鼠标按下时改变样式', () => {
      render(<SecondaryButton mode="instant" />);
      const button = screen.getByRole('button');
      
      fireEvent.mouseDown(button);
      // 注意：由于状态更新是异步的，这里可能需要使用waitFor
    });

    test('应该在鼠标松开时触发onRelease事件', () => {
      const onRelease = jest.fn();
      render(<SecondaryButton mode="instant" onRelease={onRelease} />);
      
      const button = screen.getByRole('button');
      fireEvent.mouseDown(button);
      fireEvent.mouseUp(button);
      
      expect(onRelease).toHaveBeenCalled();
    });
  });

  // 悬停效果测试
  describe('悬停效果', () => {
    test('应该在鼠标悬停时触发onHover事件', () => {
      const onHover = jest.fn();
      render(<SecondaryButton onHover={onHover} />);
      
      const button = screen.getByRole('button');
      
      fireEvent.mouseEnter(button);
      expect(onHover).toHaveBeenCalledWith(true);
      
      fireEvent.mouseLeave(button);
      expect(onHover).toHaveBeenCalledWith(false);
    });
  });

  // 禁用状态测试
  describe('禁用状态', () => {
    test('应该在禁用时不响应点击事件', () => {
      const onClick = jest.fn();
      render(<SecondaryButton disabled onClick={onClick} />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(onClick).not.toHaveBeenCalled();
      expect(button).toBeDisabled();
    });

    test('应该显示禁用样式', () => {
      render(<SecondaryButton disabled />);
      const button = screen.getByRole('button');
      
      expect(button).toHaveStyle({
        opacity: '0.6',
        cursor: 'not-allowed'
      });
    });
  });

  // 自定义样式测试
  describe('自定义样式', () => {
    test('应该应用自定义className', () => {
      render(<SecondaryButton className="custom-class" />);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('custom-class');
    });

    test('应该应用自定义style', () => {
      const customStyle = { width: '300px', height: '60px' };
      render(<SecondaryButton style={customStyle} />);
      const button = screen.getByRole('button');
      
      expect(button).toHaveStyle(customStyle);
    });
  });
});

// 事件管理器测试
describe('SecondaryButtonEventManager', () => {
  test('应该正确初始化', () => {
    const manager = new SecondaryButtonEventManager({
      mode: 'toggle',
      initialActive: false
    });
    
    const state = manager.getState();
    expect(state.isActive).toBe(false);
    expect(state.isHovered).toBe(false);
    expect(state.isPressed).toBe(false);
  });

  test('应该在toggle模式下正确切换状态', () => {
    const onToggle = jest.fn();
    const manager = new SecondaryButtonEventManager(
      { mode: 'toggle' },
      { onToggle }
    );
    
    manager.handleClick();
    expect(onToggle).toHaveBeenCalledWith(true);
    
    manager.handleClick();
    expect(onToggle).toHaveBeenCalledWith(false);
  });

  test('应该在instant模式下正确处理按下和松开', () => {
    const onPress = jest.fn();
    const onRelease = jest.fn();
    const manager = new SecondaryButtonEventManager(
      { mode: 'instant' },
      { onPress, onRelease }
    );
    
    manager.handleClick();
    expect(onPress).toHaveBeenCalled();
    
    manager.handleMouseDown();
    manager.handleMouseUp();
    expect(onRelease).toHaveBeenCalled();
  });
});
