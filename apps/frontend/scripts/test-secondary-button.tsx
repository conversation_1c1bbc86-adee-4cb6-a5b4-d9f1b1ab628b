import React, { useState } from 'react';
import { SecondaryButton } from '../../../frontend/ButtonCodes';

// 普通按键功能测试组件
export const SecondaryButtonTest: React.FC = () => {
  const [toggleState, setToggleState] = useState(false);
  const [instantPressCount, setInstantPressCount] = useState(0);
  const [hoverState, setHoverState] = useState(false);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>普通按键功能测试</h1>
      
      {/* 持续状态模式测试 */}
      <div style={{ marginBottom: '30px' }}>
        <h2>持续状态模式 (Toggle)</h2>
        <p>当前状态: {toggleState ? '激活' : '未激活'}</p>
        <SecondaryButton
          mode="toggle"
          text="切换按键"
          onToggle={(isActive) => {
            setToggleState(isActive);
            console.log('Toggle状态变更:', isActive);
          }}
          onHover={(isHovered) => {
            console.log('Toggle悬停状态:', isHovered);
          }}
        />
      </div>

      {/* 瞬时状态模式测试 */}
      <div style={{ marginBottom: '30px' }}>
        <h2>瞬时状态模式 (Instant)</h2>
        <p>按下次数: {instantPressCount}</p>
        <SecondaryButton
          mode="instant"
          text="瞬时按键"
          onPress={() => {
            setInstantPressCount(prev => prev + 1);
            console.log('瞬时按键被按下');
          }}
          onRelease={() => {
            console.log('瞬时按键被松开');
          }}
          onHover={(isHovered) => {
            console.log('Instant悬停状态:', isHovered);
          }}
        />
      </div>

      {/* 外部控制状态测试 */}
      <div style={{ marginBottom: '30px' }}>
        <h2>外部控制状态测试</h2>
        <p>悬停状态: {hoverState ? '悬停中' : '未悬停'}</p>
        <div style={{ marginBottom: '10px' }}>
          <button onClick={() => setToggleState(!toggleState)}>
            外部切换状态 (当前: {toggleState ? '激活' : '未激活'})
          </button>
        </div>
        <SecondaryButton
          mode="toggle"
          text="外部控制按键"
          isActive={toggleState}
          onHover={(isHovered) => {
            setHoverState(isHovered);
          }}
        />
      </div>

      {/* 禁用状态测试 */}
      <div style={{ marginBottom: '30px' }}>
        <h2>禁用状态测试</h2>
        <SecondaryButton
          mode="toggle"
          text="禁用按键"
          disabled
          onToggle={() => {
            console.log('这个不应该被触发');
          }}
        />
      </div>

      {/* 自定义样式测试 */}
      <div style={{ marginBottom: '30px' }}>
        <h2>自定义样式测试</h2>
        <SecondaryButton
          mode="toggle"
          text="自定义样式"
          style={{
            width: '300px',
            height: '60px',
            fontSize: '24px',
            borderRadius: '10px'
          }}
          onToggle={(isActive) => {
            console.log('自定义样式按键状态:', isActive);
          }}
        />
      </div>

      {/* 子元素测试 */}
      <div style={{ marginBottom: '30px' }}>
        <h2>子元素测试</h2>
        <SecondaryButton mode="instant">
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>🎵</span>
            <span>音乐按键</span>
            <span>🎵</span>
          </div>
        </SecondaryButton>
      </div>

      {/* 测试说明 */}
      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>测试说明</h3>
        <ul>
          <li>持续状态模式：点击按键会切换并保持激活/未激活状态</li>
          <li>瞬时状态模式：点击按键会触发按下和松开事件</li>
          <li>悬停效果：鼠标悬停时按键颜色会发生变化</li>
          <li>外部控制：可以通过外部状态控制按键的激活状态</li>
          <li>禁用状态：禁用的按键不会响应任何交互</li>
          <li>自定义样式：可以覆盖默认样式</li>
          <li>子元素：可以使用自定义的子元素替代文本</li>
        </ul>
        <p><strong>请打开浏览器控制台查看事件日志</strong></p>
      </div>
    </div>
  );
};

export default SecondaryButtonTest;
