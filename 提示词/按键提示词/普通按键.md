# 普通按键

## 按键样式

**1.默认样式(style_secondary.ts):**

- 1.按键样式:
  - 1.按键名称:secondary
  - 2.按键形状:长方形
  - 3.按键高度:50px
  - 4.按键宽度:200px
  - 5.按键圆角:0px
  - 6.按键底色:#f1f1f1
  - 7.展示方式:弹性布局
  - 8.主轴对齐:水平居中
  - 9.交叉轴对齐对齐:垂直居中
  - 10.鼠标指针样式:手型
- 2.文字样式:
  - 1.默认文本:按键
  - 2.字体大小:20px
  - 3.字体颜色:#242424
  - 4.字体对齐:居中
  - 5.字体行高:1.5

## 按键互动

**1.按键行为(event_secondary.ts):**

- 1.持续状态模式:
  - 1.状态(mode:toggle):点击后切换并保持‘激活’/‘未激活’状态
- 2.瞬时状态模式:
  - 1.状态(mode:instant):点击按键‘按下’松开按键‘回弹’状态
- 3.默认设置:
  - 1.默认状态:持续状态模式

**3.持续状态模式样式(event_secondary.ts):**

- 1.激活状态(isActive: true):
  - 1.按键底色:#929292
  - 2.激活状态下悬停:
    - 1.按键底色:#858585
- 2.未激活状态(isActive: false):
  - 1.按键底色:#f1f1f1
  - 2.未激活状态下悬停:
    - 1.按键底色:#e4e4e4

**2.瞬时状态模式样式(event_secondary.ts):**

- 1.鼠标悬停:
  - 1.按键底色:#e4e4e4
- 2.点击按下(Active: true):
  - 1.按键底色:#858585
- 3.松开回弹(Active: false):
  - 1.按键底色:#f1f1f1

## 按键组件

- **1.创建普通按键组件(SecondaryButton.tsx):**

- 1.组件应接收'mode'和'isActive'/'Active'等属性来控制其行为和样式
- 2.组件内部需处理点击事件，并根据不同模式调用外部函数

- **2.导出按键组件(index.ts):**

- 1.导出`SecondaryButton`组件及相关的类型定义
