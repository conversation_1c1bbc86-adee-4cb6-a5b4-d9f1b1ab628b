# 三级容器(矩阵系统)

## 基于‘compnetA1.tsx’容器布局

**1.组件布局(logicA1.tsx):**

- 1.装载容器:'componetA1'
- 2.存放组件:'featureA1'
- 3.排列方式:网格排列
  - 1.网格行数:33
  - 2.网格列数:33
- 4.组件间隔:'margin: componetA1宽度的0.5%'

## 功能组件定义

**1.功能组件1(featureA1.tsx):**

- 1.组件形状: 矩形
- 2.组件高度:('componetA1'容器高度 - 网格行数 + 1 * 组件间隔) / 网格行数
- 3.组件宽度:('componetA1'容器宽度 - 网格列数 + 1 * 组件间隔) / 网格列数
- 4.背景颜色:#cecece
- 5.组件圆角:5px
- 6.展示方式:弹性布局
- 7.弹性方向:垂直
- 8.对齐方式:水平，垂直居中
- 9.溢出处理:隐藏
- 10.组件定位:相对定位
- 11.鼠标指针:手型

**2.文字样式:**

- 1.默认文本:无
- 2.字体大小:随组件大小变化
- 3.字体颜色:#242424
- 4.字体对齐:居中

## 组件交互逻辑

**1.状态初始化(logicA1.tsx):**

- 1.监听'store.ts'中的‘初始化’事件
- 2.‘初始化‘事件触发’
  - 1.将所有‘featureA1’组件的‘激活’状态设置为‘false’
  - 2.清除所有组件的‘高亮边框’

**2.坐标显示(logicA1.tsx):**

- 1.访问‘store.ts’中‘坐标按键’的状态('true'/'false')
- 2.状态为‘true’时:
  - 1.计算每个组件的坐标 (以中心组件为原点 '0,0')
  - 2.将坐标字符串(如: '-1,0','1,0')作为文本显示在对应组件内部
- 3.状态为‘false’时:
  - 1.隐藏所有组件坐标文本

**3.组件点击(logicA1.tsx):**

- 1.激活状态(独立开关)
  - 1.点击后切换并保持‘激活’/‘未激活’状态
    - 1.'激活'状态
      - 1.背景颜色:#929292
    - 2.'未激活'状态
      - 1.背景颜色:#ffffff
  - 2.此状态不改变其他组件状态
- 2.高亮边框(互斥单选)
  - 1.点击任意组件产生高亮边框
    - 1.移除其他组件的高亮边框
  - 2.高亮边框样式:
    - 1.边框宽度:3px
    - 2.边框颜色:#ffd500

**4.鼠标悬停(logicA1.tsx):**

- 1.样式:
  - 1.组件: 悬浮前置
  - 2.组件大小:放大1.2倍
