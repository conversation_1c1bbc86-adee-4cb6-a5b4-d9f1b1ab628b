# 三级容器(矩阵)

## 基于‘compnetB1.tsx’容器布局

**1.布局结构说明(componetB1.tsx):**

- 1.排列方式:功能容器在‘componetB1’容器内垂直排列
- 2.排列结构:[featureB1_1]

**2.功能容器2(featureB1_1.tsx):**

- 1.容器样式:
  - 1.容器形状:长方形
  - 2.容器高度:继承父容器‘componetB1’的15%高度
  - 3.容器宽度:继承父容器'componetB1'的94%宽度
  - 4.背景颜色:#bebebe
  - 5.组件定位:相对定位
  - 6.溢出处理:隐藏
  - 7.顶部对齐:‘top:占‘componetB1’容器高的6%'
- 2.文本样式:
  - 1.默认文本:‘矩阵’
  - 2.字体大小:30px
  - 3.字体颜色:#242424
- 3.文本位置:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_1高度的20%'
  - 3.左部对齐:'left:featureB1_1宽度的1%'

**3.按键调用(featureB1_1.tsx):**

- 1.初始化按键:
  - 1.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:占'featureB1_1'容器高的35%
      - 2.键宽:占'featureB1_1'容器宽的44%
      - 3.模式:mode:instant
    - 2.文本:
      - 1.键内文本: '初始化'
      - 2.文本大小: 尺寸自适应
- 2.坐标按键:
  - 1.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:占'featureB1_1'容器高的35%
      - 2.键宽:占'featureB1_1'容器宽的44%
      - 3.模式:mode:toggle
    - 2.文本:
      - 1.键内文本: '坐标'
      - 2.文本大小: 尺寸自适应

**4.按键布局(featureB1_1.tsx):**

- 1.初始化按键:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_1高度的55%'
  - 3.左部对齐:'left:featureB1_1宽度的4%'
- 2.坐标按键:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_1高度的55%'
  - 3.右部对齐:'right:featureB1_1宽度的4%'

**5.按键业务逻辑(logicB1_1):**

- 1.状态管理:
  - 1.‘坐标按键’的`true/false`状态信息存储于‘store.ts’
- 2.按键交互业务:
  - 1.点击‘初始化按键’(一次性事件):
    - 1.向‘store.ts’或通过事件总线发出一个“初始化”事件
    - 2.自身不记录持续状态，仅触发动作
    - 3.将‘坐标按键’在‘store.ts’中的状态重置为‘false’
  - 2.点击‘坐标按键’(状态切换):
    - 1.在‘store.ts’中切换并记录自身的‘true/false’状态
