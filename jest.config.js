// Jest configuration for testing
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  testMatch: [
    '<rootDir>/apps/frontend/tests/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/frontend/**/*.test.{js,jsx,ts,tsx}',
  ],
  collectCoverageFrom: [
    'frontend/**/*.{js,jsx,ts,tsx}',
    'apps/frontend/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
}

module.exports = customJestConfig
