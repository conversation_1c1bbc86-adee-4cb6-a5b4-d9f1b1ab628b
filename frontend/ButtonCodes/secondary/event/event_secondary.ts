// 普通按键事件处理

export type ButtonMode = 'toggle' | 'instant';

export interface SecondaryButtonState {
  isActive: boolean;
  isHovered: boolean;
  isPressed: boolean;
}

export interface SecondaryButtonEventHandlers {
  onClick?: () => void;
  onToggle?: (isActive: boolean) => void;
  onPress?: () => void;
  onRelease?: () => void;
  onHover?: (isHovered: boolean) => void;
}

export interface SecondaryButtonEventConfig {
  mode: ButtonMode;
  initialActive?: boolean;
  disabled?: boolean;
}

// 普通按键事件管理类
export class SecondaryButtonEventManager {
  private state: SecondaryButtonState;
  private config: SecondaryButtonEventConfig;
  private handlers: SecondaryButtonEventHandlers;

  constructor(
    config: SecondaryButtonEventConfig,
    handlers: SecondaryButtonEventHandlers = {}
  ) {
    this.config = {
      mode: 'toggle', // 默认为持续状态模式
      initialActive: false,
      disabled: false,
      ...config
    };
    
    this.handlers = handlers;
    
    this.state = {
      isActive: this.config.initialActive || false,
      isHovered: false,
      isPressed: false
    };
  }

  // 获取当前状态
  getState(): SecondaryButtonState {
    return { ...this.state };
  }

  // 处理点击事件
  handleClick = (): void => {
    if (this.config.disabled) return;

    if (this.config.mode === 'toggle') {
      // 持续状态模式：切换激活状态
      this.state.isActive = !this.state.isActive;
      this.handlers.onToggle?.(this.state.isActive);
    } else if (this.config.mode === 'instant') {
      // 瞬时状态模式：触发点击事件
      this.handlers.onPress?.();
    }

    this.handlers.onClick?.();
  };

  // 处理鼠标按下事件
  handleMouseDown = (): void => {
    if (this.config.disabled) return;

    if (this.config.mode === 'instant') {
      this.state.isPressed = true;
    }
  };

  // 处理鼠标松开事件
  handleMouseUp = (): void => {
    if (this.config.disabled) return;

    if (this.config.mode === 'instant') {
      this.state.isPressed = false;
      this.handlers.onRelease?.();
    }
  };

  // 处理鼠标悬停事件
  handleMouseEnter = (): void => {
    if (this.config.disabled) return;

    this.state.isHovered = true;
    this.handlers.onHover?.(true);
  };

  // 处理鼠标离开事件
  handleMouseLeave = (): void => {
    if (this.config.disabled) return;

    this.state.isHovered = false;
    this.state.isPressed = false; // 鼠标离开时重置按下状态
    this.handlers.onHover?.(false);
  };

  // 更新配置
  updateConfig(newConfig: Partial<SecondaryButtonEventConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // 更新事件处理器
  updateHandlers(newHandlers: Partial<SecondaryButtonEventHandlers>): void {
    this.handlers = { ...this.handlers, ...newHandlers };
  }

  // 重置状态
  reset(): void {
    this.state = {
      isActive: this.config.initialActive || false,
      isHovered: false,
      isPressed: false
    };
  }

  // 设置激活状态（仅在toggle模式下有效）
  setActive(isActive: boolean): void {
    if (this.config.mode === 'toggle') {
      this.state.isActive = isActive;
    }
  }
}

// 创建事件管理器的工厂函数
export const createSecondaryButtonEventManager = (
  config: SecondaryButtonEventConfig,
  handlers?: SecondaryButtonEventHandlers
): SecondaryButtonEventManager => {
  return new SecondaryButtonEventManager(config, handlers);
};
