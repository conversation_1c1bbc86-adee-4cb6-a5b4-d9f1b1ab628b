// 普通按键样式定义
export interface SecondaryButtonStyles {
  // 默认样式
  default: {
    width: string;
    height: string;
    borderRadius: string;
    backgroundColor: string;
    display: string;
    justifyContent: string;
    alignItems: string;
    cursor: string;
    fontSize: string;
    color: string;
    textAlign: string;
    lineHeight: string;
    border: string;
    outline: string;
    transition: string;
  };
  
  // 持续状态模式样式
  toggle: {
    active: {
      backgroundColor: string;
      hover: string;
    };
    inactive: {
      backgroundColor: string;
      hover: string;
    };
  };
  
  // 瞬时状态模式样式
  instant: {
    hover: string;
    active: string;
    default: string;
  };
}

// 普通按键样式配置
export const secondaryButtonStyles: SecondaryButtonStyles = {
  default: {
    width: '200px',
    height: '50px',
    borderRadius: '0px',
    backgroundColor: '#f1f1f1',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    fontSize: '20px',
    color: '#242424',
    textAlign: 'center',
    lineHeight: '1.5',
    border: 'none',
    outline: 'none',
    transition: 'background-color 0.2s ease'
  },
  
  toggle: {
    active: {
      backgroundColor: '#929292',
      hover: '#858585'
    },
    inactive: {
      backgroundColor: '#f1f1f1',
      hover: '#e4e4e4'
    }
  },
  
  instant: {
    hover: '#e4e4e4',
    active: '#858585',
    default: '#f1f1f1'
  }
};

// 获取按键样式的工具函数
export const getSecondaryButtonStyle = (
  mode: 'toggle' | 'instant',
  isActive: boolean,
  isHovered: boolean,
  isPressed: boolean
): React.CSSProperties => {
  const baseStyle = { ...secondaryButtonStyles.default };
  
  if (mode === 'toggle') {
    if (isActive) {
      baseStyle.backgroundColor = isHovered 
        ? secondaryButtonStyles.toggle.active.hover 
        : secondaryButtonStyles.toggle.active.backgroundColor;
    } else {
      baseStyle.backgroundColor = isHovered 
        ? secondaryButtonStyles.toggle.inactive.hover 
        : secondaryButtonStyles.toggle.inactive.backgroundColor;
    }
  } else if (mode === 'instant') {
    if (isPressed) {
      baseStyle.backgroundColor = secondaryButtonStyles.instant.active;
    } else if (isHovered) {
      baseStyle.backgroundColor = secondaryButtonStyles.instant.hover;
    } else {
      baseStyle.backgroundColor = secondaryButtonStyles.instant.default;
    }
  }
  
  return baseStyle;
};
