'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { getSecondaryButtonStyle } from '../style/style_secondary';
import { 
  SecondaryButtonEventManager, 
  createSecondaryButtonEventManager,
  ButtonMode,
  SecondaryButtonEventHandlers,
  SecondaryButtonEventConfig
} from '../event/event_secondary';

export interface SecondaryButtonProps {
  // 基础属性
  children?: React.ReactNode;
  text?: string;
  mode?: ButtonMode;
  isActive?: boolean;
  disabled?: boolean;
  
  // 事件处理器
  onClick?: () => void;
  onToggle?: (isActive: boolean) => void;
  onPress?: () => void;
  onRelease?: () => void;
  onHover?: (isHovered: boolean) => void;
  
  // 样式相关
  className?: string;
  style?: React.CSSProperties;
  
  // 其他属性
  id?: string;
  'data-testid'?: string;
}

export const SecondaryButton: React.FC<SecondaryButtonProps> = ({
  children,
  text = '按键',
  mode = 'toggle',
  isActive: externalIsActive,
  disabled = false,
  onClick,
  onToggle,
  onPress,
  onRelease,
  onHover,
  className,
  style,
  id,
  'data-testid': dataTestId,
  ...props
}) => {
  // 内部状态管理
  const [internalIsActive, setInternalIsActive] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [eventManager, setEventManager] = useState<SecondaryButtonEventManager | null>(null);

  // 确定当前激活状态（外部控制优先）
  const currentIsActive = externalIsActive !== undefined ? externalIsActive : internalIsActive;

  // 初始化事件管理器
  useEffect(() => {
    const config: SecondaryButtonEventConfig = {
      mode,
      initialActive: currentIsActive,
      disabled
    };

    const handlers: SecondaryButtonEventHandlers = {
      onClick,
      onToggle: (isActive: boolean) => {
        if (externalIsActive === undefined) {
          setInternalIsActive(isActive);
        }
        onToggle?.(isActive);
      },
      onPress,
      onRelease,
      onHover: (hovered: boolean) => {
        setIsHovered(hovered);
        onHover?.(hovered);
      }
    };

    const manager = createSecondaryButtonEventManager(config, handlers);
    setEventManager(manager);

    return () => {
      setEventManager(null);
    };
  }, [mode, disabled, onClick, onToggle, onPress, onRelease, onHover, externalIsActive]);

  // 处理点击事件
  const handleClick = useCallback(() => {
    if (!eventManager || disabled) return;
    eventManager.handleClick();
  }, [eventManager, disabled]);

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(() => {
    if (!eventManager || disabled) return;
    eventManager.handleMouseDown();
    const state = eventManager.getState();
    setIsPressed(state.isPressed);
  }, [eventManager, disabled]);

  // 处理鼠标松开事件
  const handleMouseUp = useCallback(() => {
    if (!eventManager || disabled) return;
    eventManager.handleMouseUp();
    const state = eventManager.getState();
    setIsPressed(state.isPressed);
  }, [eventManager, disabled]);

  // 处理鼠标进入事件
  const handleMouseEnter = useCallback(() => {
    if (!eventManager || disabled) return;
    eventManager.handleMouseEnter();
  }, [eventManager, disabled]);

  // 处理鼠标离开事件
  const handleMouseLeave = useCallback(() => {
    if (!eventManager || disabled) return;
    eventManager.handleMouseLeave();
    const state = eventManager.getState();
    setIsPressed(state.isPressed);
  }, [eventManager, disabled]);

  // 获取按键样式
  const buttonStyle = getSecondaryButtonStyle(
    mode,
    currentIsActive,
    isHovered,
    isPressed
  );

  // 合并样式
  const finalStyle = {
    ...buttonStyle,
    ...(disabled && { 
      opacity: 0.6, 
      cursor: 'not-allowed' 
    }),
    ...style
  };

  return (
    <button
      id={id}
      data-testid={dataTestId}
      className={className}
      style={finalStyle}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      disabled={disabled}
      type="button"
      {...props}
    >
      {children || text}
    </button>
  );
};

export default SecondaryButton;
