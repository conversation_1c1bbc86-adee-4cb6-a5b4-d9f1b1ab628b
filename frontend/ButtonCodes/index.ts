// 按键组件统一导出入口

// 导出普通按键组件
export { SecondaryButton, default as SecondaryButtonDefault } from './secondary/SecondaryButton/SecondaryButton';

// 导出普通按键相关类型
export type { SecondaryButtonProps } from './secondary/SecondaryButton/SecondaryButton';

// 导出普通按键样式
export { 
  secondaryButtonStyles, 
  getSecondaryButtonStyle,
  type SecondaryButtonStyles 
} from './secondary/style/style_secondary';

// 导出普通按键事件管理
export {
  SecondaryButtonEventManager,
  createSecondaryButtonEventManager,
  type ButtonMode,
  type SecondaryButtonState,
  type SecondaryButtonEventHandlers,
  type SecondaryButtonEventConfig
} from './secondary/event/event_secondary';
