import React from 'react';
import ComponentA1 from './componetA1';
import ComponentInteractionB from '../interaction/componet_interactionB';

const MainContainer: React.FC = () => {
  return (
    <div style={{
      height: '100vh',
      width: '100vw',
      backgroundColor: '#242424',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden'
    }}>
      <ComponentA1 />
      <ComponentInteractionB />
    </div>
  );
};

export default MainContainer;
