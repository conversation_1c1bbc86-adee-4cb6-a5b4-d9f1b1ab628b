'use client';

import React from 'react';
import SecondaryButton from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';

interface ComponentButtonProps {
  onModeClick: () => void;
  onBusinessClick: () => void;
  isModeActive: boolean;
  isBusinessActive: boolean;
}

const ComponentButton: React.FC<ComponentButtonProps> = ({
  onModeClick,
  onBusinessClick,
  isModeActive,
  isBusinessActive
}) => {
  return (
    <div style={{
      height: '3vh',
      width: '20vw',
      backgroundColor: 'transparent',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      position: 'absolute',
      top: '0',
      left: '0'
    }}>
      <SecondaryButton
        text="模式"
        mode="instant"
        onClick={onModeClick}
        disabled={isModeActive}
        style={{
          height: '100%',
          width: '50%',
          fontSize: 'clamp(8px, 1.5vw, 16px)'
        }}
      />
      <SecondaryButton
        text="业务"
        mode="instant"
        onClick={onBusinessClick}
        disabled={isBusinessActive}
        style={{
          height: '100%',
          width: '50%',
          fontSize: 'clamp(8px, 1.5vw, 16px)'
        }}
      />
    </div>
  );
};

export default ComponentButton;
