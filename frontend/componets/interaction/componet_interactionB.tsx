'use client';

import React from 'react';
import { useContainerStore } from '../../Store/store';
import ComponentB1 from '../componet/componetB1';
import ComponentB2 from '../componet/componetB2';
import ComponentButton from '../componet/componetButton';

const ComponentInteractionB: React.FC = () => {
  const { activeContainer, setActiveContainer } = useContainerStore();

  const handleModeClick = () => {
    setActiveContainer('mode');
  };

  const handleBusinessClick = () => {
    setActiveContainer('business');
  };

  return (
    <div style={{
      height: '95vh',
      width: '20vw',
      position: 'relative',
      marginLeft: '1vw'
    }}>
      {/* 按键容器 - 始终可见 */}
      <ComponentButton
        onModeClick={handleModeClick}
        onBusinessClick={handleBusinessClick}
        isModeActive={activeContainer === 'mode'}
        isBusinessActive={activeContainer === 'business'}
      />
      
      {/* 根据状态显示不同的容器 */}
      {activeContainer === 'mode' && <ComponentB1 />}
      {activeContainer === 'business' && <ComponentB2 />}
    </div>
  );
};

export default ComponentInteractionB;
